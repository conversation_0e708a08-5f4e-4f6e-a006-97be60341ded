/**
 * 滑动面板闪烁修复测试脚本
 * 🎯 核心价值：验证矩阵系统、格子单击、选词等交互不再导致滑动面板闪烁
 * 📦 功能范围：自动化测试各种交互场景
 * 🔄 架构设计：基于Playwright的端到端测试
 */

// 测试场景配置
const TEST_SCENARIOS = [
  {
    name: '格子单击测试',
    description: '连续点击多个格子，检查滑动面板是否闪烁',
    actions: [
      { type: 'click', selector: '[data-x="5"][data-y="5"]' },
      { type: 'wait', duration: 100 },
      { type: 'click', selector: '[data-x="10"][data-y="10"]' },
      { type: 'wait', duration: 100 },
      { type: 'click', selector: '[data-x="15"][data-y="15"]' },
      { type: 'wait', duration: 100 }
    ]
  },
  {
    name: '选词模式测试',
    description: '激活选词模式，检查滑动面板是否稳定',
    actions: [
      { type: 'click', selector: '[data-x="8"][data-y="8"]' },
      { type: 'wait', duration: 200 },
      { type: 'doubleClick', selector: '[data-x="8"][data-y="8"]' },
      { type: 'wait', duration: 500 },
      { type: 'key', key: 'Escape' }
    ]
  },
  {
    name: '模式切换测试',
    description: '切换显示模式，检查滑动面板响应',
    actions: [
      { type: 'click', selector: '[data-testid="mode-selector"]' },
      { type: 'wait', duration: 200 },
      { type: 'click', selector: '[data-value="color-word"]' },
      { type: 'wait', duration: 300 }
    ]
  },
  {
    name: '响应式面板测试',
    description: '调整窗口大小，检查面板响应式行为',
    actions: [
      { type: 'resize', width: 800, height: 600 },
      { type: 'wait', duration: 300 },
      { type: 'resize', width: 1200, height: 800 },
      { type: 'wait', duration: 300 }
    ]
  }
];

// 闪烁检测函数
function detectFlicker() {
  return new Promise((resolve) => {
    const panel = document.querySelector('.controls-sidebar, .fixed.top-4.right-4');
    if (!panel) {
      resolve({ hasFlicker: false, reason: 'Panel not found' });
      return;
    }

    let flickerCount = 0;
    let lastVisibility = panel.style.visibility;
    let lastOpacity = panel.style.opacity;
    
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes') {
          const currentVisibility = panel.style.visibility;
          const currentOpacity = panel.style.opacity;
          
          if (currentVisibility !== lastVisibility || currentOpacity !== lastOpacity) {
            flickerCount++;
            lastVisibility = currentVisibility;
            lastOpacity = currentOpacity;
          }
        }
      });
    });

    observer.observe(panel, {
      attributes: true,
      attributeFilter: ['style', 'class']
    });

    // 监听3秒
    setTimeout(() => {
      observer.disconnect();
      resolve({
        hasFlicker: flickerCount > 2, // 超过2次变化认为是闪烁
        flickerCount,
        reason: flickerCount > 2 ? `检测到${flickerCount}次样式变化` : '面板稳定'
      });
    }, 3000);
  });
}

// 性能监控函数
function monitorPerformance() {
  const startTime = performance.now();
  let renderCount = 0;
  
  const observer = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach((entry) => {
      if (entry.entryType === 'measure' && entry.name.includes('React')) {
        renderCount++;
      }
    });
  });
  
  observer.observe({ entryTypes: ['measure'] });
  
  return {
    stop: () => {
      observer.disconnect();
      const endTime = performance.now();
      return {
        duration: endTime - startTime,
        renderCount,
        avgRenderTime: renderCount > 0 ? (endTime - startTime) / renderCount : 0
      };
    }
  };
}

// 主测试函数
async function runFlickerTest() {
  console.log('🚀 开始滑动面板闪烁修复测试');
  
  const results = [];
  
  for (const scenario of TEST_SCENARIOS) {
    console.log(`\n📋 测试场景: ${scenario.name}`);
    console.log(`📝 描述: ${scenario.description}`);
    
    const performanceMonitor = monitorPerformance();
    const flickerPromise = detectFlicker();
    
    // 执行测试动作
    for (const action of scenario.actions) {
      try {
        switch (action.type) {
          case 'click':
            const element = document.querySelector(action.selector);
            if (element) {
              element.click();
              console.log(`✅ 点击: ${action.selector}`);
            } else {
              console.log(`❌ 元素未找到: ${action.selector}`);
            }
            break;
            
          case 'doubleClick':
            const dbElement = document.querySelector(action.selector);
            if (dbElement) {
              dbElement.dispatchEvent(new MouseEvent('dblclick', { bubbles: true }));
              console.log(`✅ 双击: ${action.selector}`);
            }
            break;
            
          case 'key':
            document.dispatchEvent(new KeyboardEvent('keydown', { key: action.key }));
            console.log(`✅ 按键: ${action.key}`);
            break;
            
          case 'resize':
            window.resizeTo(action.width, action.height);
            console.log(`✅ 调整窗口: ${action.width}x${action.height}`);
            break;
            
          case 'wait':
            await new Promise(resolve => setTimeout(resolve, action.duration));
            console.log(`⏱️ 等待: ${action.duration}ms`);
            break;
        }
      } catch (error) {
        console.log(`❌ 动作执行失败: ${error.message}`);
      }
    }
    
    // 等待检测完成
    const flickerResult = await flickerPromise;
    const performanceResult = performanceMonitor.stop();
    
    const scenarioResult = {
      scenario: scenario.name,
      flicker: flickerResult,
      performance: performanceResult,
      passed: !flickerResult.hasFlicker
    };
    
    results.push(scenarioResult);
    
    console.log(`📊 结果: ${scenarioResult.passed ? '✅ 通过' : '❌ 失败'}`);
    console.log(`🔍 闪烁检测: ${flickerResult.reason}`);
    console.log(`⚡ 性能: ${performanceResult.renderCount}次渲染, 平均${performanceResult.avgRenderTime.toFixed(2)}ms`);
  }
  
  // 生成测试报告
  console.log('\n📈 测试报告汇总:');
  console.log('='.repeat(50));
  
  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;
  
  console.log(`📊 总体结果: ${passedTests}/${totalTests} 测试通过`);
  console.log(`✅ 通过率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.scenario}`);
    console.log(`   状态: ${result.passed ? '✅ 通过' : '❌ 失败'}`);
    console.log(`   闪烁: ${result.flicker.reason}`);
    console.log(`   渲染: ${result.performance.renderCount}次`);
  });
  
  if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！滑动面板闪烁问题已修复。');
  } else {
    console.log('\n⚠️ 部分测试失败，需要进一步优化。');
  }
  
  return results;
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runFlickerTest, detectFlicker, monitorPerformance };
} else {
  // 浏览器环境下自动运行
  window.runFlickerTest = runFlickerTest;
  console.log('💡 在浏览器控制台中运行 runFlickerTest() 开始测试');
}
