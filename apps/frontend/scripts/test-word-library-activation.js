/**
 * 测试词库激活功能
 * 验证双击激活时的空词库检查和滑动功能
 */

// 等待页面加载完成
function waitForPageLoad() {
  return new Promise((resolve) => {
    if (document.readyState === 'complete') {
      resolve();
    } else {
      window.addEventListener('load', resolve);
    }
  });
}

// 等待元素出现
function waitForElement(selector, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element ${selector} not found within ${timeout}ms`));
    }, timeout);
  });
}

// 模拟双击事件
function simulateDoubleClick(element) {
  const event = new MouseEvent('dblclick', {
    view: window,
    bubbles: true,
    cancelable: true,
    clientX: element.getBoundingClientRect().left + element.offsetWidth / 2,
    clientY: element.getBoundingClientRect().top + element.offsetHeight / 2
  });
  element.dispatchEvent(event);
}

// 检查Toast是否显示
function checkToastMessage(expectedMessage) {
  return new Promise((resolve) => {
    // 检查多种可能的Toast选择器
    const toastSelectors = [
      '[role="alert"]',
      '.fixed.top-1\\/2.left-1\\/2',
      '.toast',
      '[data-testid="toast"]'
    ];

    let found = false;
    for (const selector of toastSelectors) {
      const toasts = document.querySelectorAll(selector);
      for (const toast of toasts) {
        if (toast.textContent && toast.textContent.includes(expectedMessage)) {
          console.log('✅ 找到预期的Toast消息:', toast.textContent);
          found = true;
          resolve(true);
          return;
        }
      }
    }

    if (!found) {
      // 等待一段时间后再检查
      setTimeout(() => {
        for (const selector of toastSelectors) {
          const toasts = document.querySelectorAll(selector);
          for (const toast of toasts) {
            if (toast.textContent && toast.textContent.includes(expectedMessage)) {
              console.log('✅ 延迟找到预期的Toast消息:', toast.textContent);
              resolve(true);
              return;
            }
          }
        }
        console.log('❌ 未找到预期的Toast消息:', expectedMessage);
        resolve(false);
      }, 1000);
    }
  });
}

// 主测试函数
async function testWordLibraryActivation() {
  try {
    console.log('🚀 开始测试词库激活功能...');

    // 等待页面加载
    await waitForPageLoad();
    console.log('✅ 页面加载完成');

    // 等待矩阵元素出现
    await waitForElement('.matrix-cell', 10000);
    console.log('✅ 矩阵元素已加载');

    // 等待一段时间确保所有组件都已初始化
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 查找一个矩阵单元格进行测试
    const matrixCells = document.querySelectorAll('.matrix-cell');
    if (matrixCells.length === 0) {
      throw new Error('未找到矩阵单元格');
    }

    // 选择一个有颜色数据的单元格（避免选择第一个可能没有数据的单元格）
    let testCell = null;
    for (let i = 10; i < Math.min(50, matrixCells.length); i++) {
      const cell = matrixCells[i];
      if (cell.dataset.x && cell.dataset.y) {
        testCell = cell;
        break;
      }
    }

    if (!testCell) {
      throw new Error('未找到合适的测试单元格');
    }

    console.log(`✅ 找到测试单元格: (${testCell.dataset.x}, ${testCell.dataset.y})`);

    // 模拟双击激活
    console.log('🖱️ 模拟双击单元格...');
    simulateDoubleClick(testCell);

    // 等待并检查Toast消息
    console.log('⏳ 等待Toast消息...');
    const toastFound = await checkToastMessage('请先填入词语');

    if (toastFound) {
      console.log('🎉 测试成功！空词库检查功能正常工作');
    } else {
      console.log('⚠️ 可能是非空词库或Toast消息格式不同');
      
      // 检查是否有其他Toast消息
      const allToasts = document.querySelectorAll('[role="alert"], .fixed, .toast');
      if (allToasts.length > 0) {
        console.log('📋 当前页面上的Toast消息:');
        allToasts.forEach((toast, index) => {
          console.log(`  ${index + 1}. ${toast.textContent}`);
        });
      }
    }

    // 检查词库是否滑动到可视区域
    console.log('🔍 检查词库滑动功能...');
    const activeLibrary = document.querySelector('.word-library-active');
    if (activeLibrary) {
      console.log('✅ 找到激活的词库，滑动功能可能正常工作');
      
      // 检查是否在可视区域内
      const rect = activeLibrary.getBoundingClientRect();
      const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;
      console.log(`📍 词库可见性: ${isVisible ? '可见' : '不可见'}`);
    } else {
      console.log('ℹ️ 未找到激活的词库（可能是空词库被阻止激活）');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果在浏览器环境中运行，自动开始测试
if (typeof window !== 'undefined') {
  // 等待一段时间后开始测试，确保页面完全加载
  setTimeout(testWordLibraryActivation, 3000);
}

// 导出测试函数供手动调用
window.testWordLibraryActivation = testWordLibraryActivation;

console.log('📝 测试脚本已加载。可以手动调用 testWordLibraryActivation() 进行测试');
