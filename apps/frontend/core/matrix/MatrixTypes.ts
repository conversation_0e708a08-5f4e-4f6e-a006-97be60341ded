/**
 * 矩阵核心类型定义
 * 🎯 核心价值：统一的类型系统，支持数据驱动视图和业务模式切换
 * 📦 功能范围：矩阵数据、渲染配置、业务模式、计算属性
 * 🔄 架构设计：基于数据驱动的类型设计，支持高性能计算属性
 */

// ===== 基础矩阵类型 =====

/** 矩阵维度常量 */
export const MATRIX_SIZE = 33;
export const TOTAL_CELLS = MATRIX_SIZE * MATRIX_SIZE; // 1089

/** 坐标类型 */
export interface Coordinate {
  x: number;
  y: number;
}

/** 基础颜色类型（包含黑色） */
export type BasicColorType = 'black' | 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink';

/** 组类型 */
export type GroupType = 'A' | 'B' | 'C' | 'D' | 'E' | 'F' | 'G' | 'H' | 'I' | 'J' | 'K' | 'L' | 'M';

/** 数据级别 */
export type DataLevel = 1 | 2 | 3 | 4;

/** 主模式：决定是否使用数据颜色 */
export type MainMode = 'default' | 'color';

/** 内容模式：决定显示什么内容 */
export type ContentMode = 'blank' | 'index' | 'coordinate' | 'level' | 'mapping' | 'word';

/** 业务模式（保持向后兼容） */
export type BusinessMode = 'coordinate' | 'color' | 'level' | 'word';

/** 模式配置 */
export interface ModeConfig {
  mainMode: MainMode;
  contentMode: ContentMode;
}

/** 数据可用性检测 */
export interface DataAvailability {
  hasCoordinateData: boolean;
  hasLevelData: boolean;
  hasMappingData: boolean;
  hasWordData: boolean;
}

// ===== 颜色值定义 =====

/** 颜色值详细信息 */
export interface ColorValue {
  name: string;
  hex: string;
  rgb: [number, number, number];
  hsl: [number, number, number];
  mappingValue?: number | 'group'; // 数字映射值或组字符映射（黑色使用'group'）
}

// ===== 组偏移配置 =====

/** 组偏移配置 */
export interface GroupOffsetConfig {
  defaultOffset: [number, number];
  level1Offsets?: Record<BasicColorType, [number, number]>;
}

// ===== 单元格数据 =====

/** 单元格基础数据 */
export interface CellData {
  x: number;
  y: number;
  color?: BasicColorType;
  level?: DataLevel;
  value?: number;
  word?: string;
  isActive: boolean;
  isSelected: boolean;
  isHovered: boolean;
}

/** 单元格渲染数据 */
export interface CellRenderData {
  content: string;
  style: CellStyle;
  className: string;
  isInteractive: boolean;
}

/** 单元格样式 */
export interface CellStyle {
  backgroundColor?: string;
  color?: string;
  border?: string;
  borderRadius?: string;
  opacity?: number;
  transform?: string;
  fontSize?: string;
  fontWeight?: string;
  boxShadow?: string;
  transition?: string;
}

// ===== 矩阵状态 =====

/** 矩阵数据 */
export interface MatrixData {
  cells: Map<string, CellData>; // key: "x,y"
  selectedCells: Set<string>;
  hoveredCell: string | null;
  focusedCell: string | null;
}

/** 矩阵配置 */
export interface MatrixConfig {
  mode: BusinessMode; // 保持向后兼容
  mainMode?: MainMode;
  contentMode?: ContentMode;
}

// ===== 业务模式配置 =====

/** 业务模式处理器 - 简化版本 */
export interface ModeHandler {
  processData: (data: MatrixData) => ProcessedMatrixData;
  renderCell: (cell: CellData) => CellRenderData;
  handleInteraction: (event: InteractionEvent, cell: CellData) => void;
}

/** 处理后的矩阵数据 */
export interface ProcessedMatrixData {
  cells: Map<string, CellData>;
  renderData: Map<string, CellRenderData>;
  metadata: {
    totalCells: number;
    activeCells: number;
    selectedCells: number;
    mode: BusinessMode;
  };
}

// ===== 交互事件 =====

/** 交互事件类型 */
export type InteractionEventType = 'click' | 'doubleClick' | 'hover' | 'focus' | 'keydown';

/** 交互事件 */
export interface InteractionEvent {
  type: InteractionEventType;
  coordinate: Coordinate;
  modifiers: {
    ctrl: boolean;
    shift: boolean;
    alt: boolean;
  };
  data?: any;
}

// ===== 计算属性 =====

/** 计算属性函数 */
export type ComputedProperty<T> = () => T;

/** 计算属性缓存 */
export interface ComputedCache {
  cellStyles: Map<string, CellStyle>;
  cellContents: Map<string, string>;
  cellClassNames: Map<string, string>;
  interactionStates: Map<string, boolean>;
  lastUpdate: number;
}





// ===== 词库管理类型 =====

/** 词库条目 */
export interface WordEntry {
  /** 唯一标识符 */
  id: string;
  /** 词语文本 */
  text: string;
  /** 所属颜色 */
  color: BasicColorType;
  /** 所属级别 */
  level: DataLevel;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
  /** 使用次数 */
  usageCount: number;
  /** 最后使用时间 */
  lastUsed?: Date;
  /** 是否为重复词语 */
  isDuplicate?: boolean;
  /** 使用位置记录 */
  usagePositions?: Array<{ x: number; y: number; timestamp: Date }>;
}

/** 词库类别键 */
export type WordLibraryKey = `${BasicColorType}-${DataLevel}`;

/** 词库数据结构 */
export interface WordLibrary {
  /** 词库标识 */
  key: WordLibraryKey;
  /** 颜色 */
  color: BasicColorType;
  /** 级别 */
  level: DataLevel;
  /** 词语列表 */
  words: WordEntry[];
  /** 是否折叠 */
  collapsed: boolean;
  /** 最后更新时间 */
  lastUpdated: Date;
}

/** 词库管理状态 */
export interface WordLibraryState {
  /** 所有词库 */
  libraries: Map<WordLibraryKey, WordLibrary>;
  /** 当前激活的词库 */
  activeLibrary: WordLibraryKey | null;
  /** 重复词语映射 */
  duplicateWords: Map<string, WordLibraryKey[]>;
  /** 词语到随机颜色的映射 */
  wordHighlightColors: Map<string, string>;
  /** 已使用的随机颜色集合 */
  usedHighlightColors: Set<string>;
  /** 全局词语索引：词语 -> 所在词库集合 */
  globalWordIndex: Map<string, Set<WordLibraryKey>>;
  /** 是否正在加载 */
  isLoading: boolean;
  /** 最后同步时间 */
  lastSyncTime: Date | null;
}

/** 词语验证结果 */
export interface WordValidationResult {
  /** 是否有效 */
  isValid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 是否重复 */
  isDuplicate: boolean;
  /** 重复的词库 */
  duplicateLibraries: WordLibraryKey[];
}

/** 填词模式状态 */
export interface WordInputState {
  /** 是否激活填词模式 */
  isActive: boolean;
  /** 当前选中的单元格 */
  selectedCell: { x: number; y: number } | null;
  /** 匹配的词库 */
  matchedLibrary: WordLibraryKey | null;
  /** 当前选中的词语索引 */
  selectedWordIndex: number;
  /** 可选词语列表 */
  availableWords: WordEntry[];
  /** 临时显示的词语（用于实时预览） */
  temporaryWord: string | null;
  /** 是否已绑定词语（用于判断退出时是否需要清除临时显示） */
  isWordBound: boolean;
}

// ===== 工具类型 =====

/** 坐标键生成器 */
export const coordinateKey = (x: number, y: number): string => `${x},${y}`;

/** 坐标解析器 */
export const parseCoordinateKey = (key: string): Coordinate => {
  const [x, y] = key.split(',').map(Number);
  return { x, y };
};

/** 词库键生成器 */
export const createWordLibraryKey = (color: BasicColorType, level: DataLevel): WordLibraryKey =>
  `${color}-${level}` as WordLibraryKey;

/** 词库键解析器 */
export const parseWordLibraryKey = (key: WordLibraryKey): { color: BasicColorType; level: DataLevel } => {
  const [color, levelStr] = key.split('-');
  return {
    color: color as BasicColorType,
    level: parseInt(levelStr) as DataLevel
  };
};

/** 默认单元格数据 */
export const createDefaultCell = (x: number, y: number): CellData => ({
  x,
  y,
  isActive: true,
  isSelected: false,
  isHovered: false,
});

/** 默认矩阵配置 */
export const DEFAULT_MATRIX_CONFIG: MatrixConfig = {
  mode: 'coordinate', // 保持向后兼容
  mainMode: 'default',
  contentMode: 'blank', // 默认空白模式，性能更好
};


