/**
 * Toast状态管理
 * 🎯 核心价值：全局Toast消息管理，支持多个消息同时显示
 * 📦 功能范围：添加、移除、清空Toast消息
 * 🔄 架构设计：基于Zustand的轻量级状态管理
 */

'use client';

import { create } from 'zustand';

// ===== 类型定义 =====

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // 毫秒，0表示不自动消失
  closable?: boolean;
}

interface ToastStore {
  /** Toast消息列表 */
  toasts: ToastMessage[];
  
  /** 添加Toast消息 */
  addToast: (toast: Omit<ToastMessage, 'id'>) => string;
  
  /** 移除Toast消息 */
  removeToast: (id: string) => void;
  
  /** 清空所有Toast消息 */
  clearToasts: () => void;
  
  /** 显示成功消息 */
  showSuccess: (title: string, message?: string, duration?: number) => string;
  
  /** 显示错误消息 */
  showError: (title: string, message?: string, duration?: number) => string;
  
  /** 显示警告消息 */
  showWarning: (title: string, message?: string, duration?: number) => string;
  
  /** 显示信息消息 */
  showInfo: (title: string, message?: string, duration?: number) => string;
}

// ===== 工具函数 =====

/** 生成唯一ID */
const generateId = (): string => {
  return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// ===== Toast Store =====

export const useToastStore = create<ToastStore>((set, get) => ({
  toasts: [],

  addToast: (toast) => {
    const id = generateId();
    const newToast: ToastMessage = {
      id,
      duration: 3000, // 默认3秒
      closable: true,
      ...toast
    };

    set((state) => ({
      toasts: [...state.toasts, newToast]
    }));

    // 自动移除（如果设置了duration且大于0）
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        get().removeToast(id);
      }, newToast.duration);
    }

    return id;
  },

  removeToast: (id) => {
    set((state) => ({
      toasts: state.toasts.filter(toast => toast.id !== id)
    }));
  },

  clearToasts: () => {
    set({ toasts: [] });
  },

  showSuccess: (title, message, duration = 3000) => {
    return get().addToast({
      type: 'success',
      title,
      message,
      duration
    });
  },

  showError: (title, message, duration = 5000) => {
    return get().addToast({
      type: 'error',
      title,
      message,
      duration
    });
  },

  showWarning: (title, message, duration = 4000) => {
    return get().addToast({
      type: 'warning',
      title,
      message,
      duration
    });
  },

  showInfo: (title, message, duration = 3000) => {
    return get().addToast({
      type: 'info',
      title,
      message,
      duration
    });
  }
}));

// ===== 便捷函数 =====

/** 显示成功消息 */
export const showSuccessToast = (title: string, message?: string, duration?: number) => {
  return useToastStore.getState().showSuccess(title, message, duration);
};

/** 显示错误消息 */
export const showErrorToast = (title: string, message?: string, duration?: number) => {
  return useToastStore.getState().showError(title, message, duration);
};

/** 显示警告消息 */
export const showWarningToast = (title: string, message?: string, duration?: number) => {
  return useToastStore.getState().showWarning(title, message, duration);
};

/** 显示信息消息 */
export const showInfoToast = (title: string, message?: string, duration?: number) => {
  return useToastStore.getState().showInfo(title, message, duration);
};

// ===== 导出类型 =====

export type { ToastStore };
