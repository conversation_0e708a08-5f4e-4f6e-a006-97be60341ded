/**
 * 填词模式事件处理Hook
 * 🎯 核心价值：统一管理填词模式下的键盘和鼠标事件
 * 📦 功能范围：键盘导航、点击空白退出、事件清理
 * 🔄 架构设计：自定义Hook，减少组件复杂度
 */

import type { Coordinate } from '@/core/matrix/MatrixTypes';
import { useEffect, useRef } from 'react';

// ===== 类型定义 =====

export interface WordInputEventHandlers {
  selectPreviousWord: () => void;
  selectNextWord: () => void;
  confirmWordSelection: () => any;
  bindWordToCell: (x: number, y: number, wordId: string) => void;
  unbindWordFromCell: (x: number, y: number) => void;
  deactivateWordInput: () => void;
}

export interface UseWordInputEventsProps {
  isWordInputActive: boolean;
  selectedCell: Coordinate | null;
  isWordBound: boolean;
  handlers: WordInputEventHandlers;
}

// ===== 自定义Hook =====

export const useWordInputEvents = ({
  isWordInputActive,
  selectedCell,
  isWordBound,
  handlers
}: UseWordInputEventsProps) => {
  const {
    selectPreviousWord,
    selectNextWord,
    confirmWordSelection,
    bindWordToCell,
    unbindWordFromCell,
    deactivateWordInput
  } = handlers;

  // 点击空白退出填词模式
  useEffect(() => {
    if (!isWordInputActive) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // 检查点击是否在填词相关元素内部
      const isClickOnWordInputCell = target.closest('.matrix-cell.word-input-active');
      const isClickOnWordLibrary = target.closest('.word-library-active') || target.closest('[data-word-library]');
      const isClickOnToast = target.closest('.fixed.top-1\\/2.left-1\\/2') || target.closest('[role="alert"]');

      // 如果点击不在相关元素内部，退出填词模式
      if (!isClickOnWordInputCell && !isClickOnWordLibrary && !isClickOnToast) {
        // 如果没有绑定词语，清除临时显示
        if (!isWordBound && selectedCell) {
          console.log('清除临时显示词语');
        }
        deactivateWordInput();
        console.log('点击空白退出填词模式');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isWordInputActive, isWordBound, selectedCell, deactivateWordInput]);

  // 键盘事件处理 - 填词模式下的快捷键
  useEffect(() => {
    if (!isWordInputActive) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          selectPreviousWord();
          break;
        case 'ArrowRight':
          event.preventDefault();
          selectNextWord();
          break;
        case 'Enter':
          event.preventDefault();
          const selectedWord = confirmWordSelection();
          if (selectedWord && selectedCell) {
            bindWordToCell(selectedCell.x, selectedCell.y, selectedWord.id);
            console.log('确认选择词语:', selectedWord);
          }
          deactivateWordInput();
          break;
        case 'Escape':
          event.preventDefault();
          // 如果没有绑定词语，清除临时显示
          if (!isWordBound && selectedCell) {
            console.log('ESC键清除临时显示词语');
          }
          deactivateWordInput();
          break;
        case 'Delete':
        case 'Backspace':
          event.preventDefault();
          if (selectedCell) {
            unbindWordFromCell(selectedCell.x, selectedCell.y);
            console.log('删除单元格词语');
          }
          deactivateWordInput();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [
    isWordInputActive,
    selectPreviousWord,
    selectNextWord,
    confirmWordSelection,
    selectedCell,
    bindWordToCell,
    deactivateWordInput,
    unbindWordFromCell,
    isWordBound
  ]);

  // Hook卸载时的清理逻辑
  useEffect(() => {
    return () => {
      console.log('useWordInputEvents Hook卸载，清理事件监听器');
    };
  }, []);
};

// ===== 防抖双击Hook =====

export interface UseDebounceDoubleClickProps {
  onDoubleClick: (coordinate: Coordinate) => Promise<void>;
  delay?: number;
}

export const useDebounceDoubleClick = ({
  onDoubleClick,
  delay = 100
}: UseDebounceDoubleClickProps) => {
  // 使用专业的防抖实现
  const debouncedFn = useRef<any>(null);

  // 创建防抖函数
  useEffect(() => {
    // 清理之前的防抖函数
    if (debouncedFn.current) {
      debouncedFn.current.cancel();
    }

    // 创建新的防抖函数 - 临时使用简单实现，稍后替换为专业版本
    let timeoutId: NodeJS.Timeout | null = null;

    const debouncedDoubleClick = async (coordinate: Coordinate) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(async () => {
        await onDoubleClick(coordinate);
      }, delay);
    };

    debouncedDoubleClick.cancel = () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    };

    debouncedFn.current = debouncedDoubleClick;

    return () => {
      if (debouncedFn.current) {
        debouncedFn.current.cancel();
      }
    };
  }, [onDoubleClick, delay]);

  return { debouncedDoubleClick: debouncedFn.current };
};
