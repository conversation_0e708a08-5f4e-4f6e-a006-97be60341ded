/**
 * Toast通知组件
 * 🎯 核心价值：简洁的消息提示，用于用户反馈
 * 📦 功能范围：错误提示、成功提示、警告提示
 * 🔄 架构设计：轻量级、自动消失、支持多种类型
 */

'use client';

import React, { useEffect, useRef, useState } from 'react';

// ===== 类型定义 =====

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastProps {
  /** 消息内容 */
  message: string;
  /** 消息类型 */
  type?: ToastType;
  /** 显示时长（毫秒） */
  duration?: number;
  /** 是否显示 */
  show: boolean;
  /** 关闭回调 */
  onClose?: () => void;
  /** 自定义类名 */
  className?: string;
}

// ===== 样式配置 =====

const toastStyles: Record<ToastType, string> = {
  success: 'bg-gray-900 border-gray-700 text-white',
  error: 'bg-gray-900 border-gray-700 text-white',
  warning: 'bg-gray-900 border-gray-700 text-white',
  info: 'bg-gray-900 border-gray-700 text-white'
};

const toastIcons: Record<ToastType, string> = {
  success: '✓',
  error: '✗',
  warning: '!',
  info: 'i'
};

// ===== Toast组件 =====

const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  show,
  onClose,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (show) {
      setIsVisible(true);
      setIsAnimating(true);
    } else {
      // 当show变为false时，开始关闭动画
      handleClose();
    }
  }, [show]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, 300); // 等待动画完成
  };

  if (!isVisible) return null;

  return (
    <div
      className={`
        fixed top-1/2 left-1/2 z-50 max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${isAnimating ? '-translate-x-1/2 -translate-y-1/2 opacity-100' : '-translate-x-1/2 -translate-y-1/2 opacity-0 scale-95'}
        ${className}
      `}
    >
      <div
        className={`
          px-6 py-4 rounded-lg border shadow-xl backdrop-blur-sm
          ${toastStyles[type]}
        `}
      >
        <div className="flex items-center justify-center">
          <div className="flex items-center space-x-3">
            <span className="text-lg font-bold w-6 h-6 flex items-center justify-center bg-white text-gray-900 rounded-full">
              {toastIcons[type]}
            </span>
            <span className="text-base font-medium">{message}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// ===== Toast Hook =====

export interface ToastState {
  message: string;
  type: ToastType;
  show: boolean;
}

export const useToast = () => {
  const [toast, setToast] = useState<ToastState>({
    message: '',
    type: 'info',
    show: false
  });

  // 使用ref来存储定时器，避免重复设置
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showToast = (message: string, type: ToastType = 'info', duration: number = 3000) => {
    // 清除之前的定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    console.log('显示Toast:', { message, type, duration });

    setToast({
      message,
      type,
      show: true
    });

    // 自动隐藏
    if (duration > 0) {
      timeoutRef.current = setTimeout(() => {
        console.log('Toast自动隐藏');
        setToast(prev => ({
          ...prev,
          show: false
        }));
        timeoutRef.current = null;
      }, duration);
    }
  };

  const hideToast = () => {
    // 清除定时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    console.log('手动隐藏Toast');
    setToast(prev => ({
      ...prev,
      show: false
    }));
  };

  const showSuccess = (message: string, duration?: number) => showToast(message, 'success', duration);
  const showError = (message: string, duration?: number) => showToast(message, 'error', duration);
  const showWarning = (message: string, duration?: number) => showToast(message, 'warning', duration);
  const showInfo = (message: string, duration?: number) => showToast(message, 'info', duration);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    toast,
    showToast,
    hideToast,
    showSuccess,
    showError,
    showWarning,
    showInfo
  };
};

// ===== 简化的内联Toast组件 =====

interface InlineToastProps {
  message: string;
  type?: ToastType;
  show: boolean;
  className?: string;
}

export const InlineToast: React.FC<InlineToastProps> = ({
  message,
  type = 'error',
  show,
  className = ''
}) => {
  if (!show) return null;

  return (
    <div
      className={`
        p-2 rounded-md border text-xs
        ${toastStyles[type]}
        animate-pulse
        ${className}
      `}
    >
      <div className="flex items-center space-x-1">
        <span>{toastIcons[type]}</span>
        <span>{message}</span>
      </div>
    </div>
  );
};



export default Toast;
