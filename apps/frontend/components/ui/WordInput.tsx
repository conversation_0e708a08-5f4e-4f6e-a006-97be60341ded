/**
 * 词库富文本输入框组件
 * 🎯 核心价值：专用于词库管理的智能输入框，支持自动分割和验证
 * 📦 功能范围：词语输入、自动分割、重复检测、颜色适配
 * 🔄 架构设计：受控组件，支持实时验证和视觉反馈
 */

'use client';

import type {
  BasicColorType,
  DataLevel,
  WordLibraryKey,
  WordValidationResult
} from '@/core/matrix/MatrixTypes';
import {
  getWordLibraryBackgroundColor,
  getWordLibraryTextColor,
  parseInputText
} from '@/core/wordLibrary/WordLibraryCore';
import { useWordLibraryStore } from '@/core/wordLibrary/WordLibraryStore';
import React, { useCallback, useEffect, useRef, useState } from 'react';

// ===== 组件属性 =====

interface WordInputProps {
  /** 词库标识 */
  libraryKey: WordLibraryKey;
  /** 颜色 */
  color: BasicColorType;
  /** 级别 */
  level: DataLevel;
  /** 是否折叠状态 */
  collapsed?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 自定义类名 */
  className?: string;
  /** 输入变化回调 */
  onChange?: (words: string[]) => void;
  /** 验证结果回调 */
  onValidation?: (results: WordValidationResult[]) => void;
}

// ===== 词语标签组件 =====

interface WordTagProps {
  text: string;
  isValid: boolean;
  isDuplicate: boolean;
  duplicateLibraries?: WordLibraryKey[];
  backgroundColor: string;
  textColor: string;
  highlightColor?: string;
  onRemove: () => void;
}

const WordTag: React.FC<WordTagProps> = ({
  text,
  isValid,
  isDuplicate,
  duplicateLibraries = [],
  backgroundColor,
  textColor,
  highlightColor,
  onRemove
}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const getTagStyle = () => {
    if (!isValid) {
      return {
        backgroundColor: '#fee2e2',
        color: '#dc2626',
        border: '1px solid #fca5a5'
      };
    }

    if (isDuplicate) {
      // 如果有高亮颜色，使用随机颜色；否则使用默认的橙色边框
      if (highlightColor) {
        return {
          backgroundColor: highlightColor,
          color: '#000000', // 黑色文字确保可读性
          border: `2px solid ${highlightColor}`,
          boxShadow: `0 0 0 1px ${highlightColor}`
        };
      } else {
        return {
          backgroundColor: backgroundColor,
          color: textColor,
          border: '2px solid #f59e0b',
          boxShadow: '0 0 0 1px #f59e0b'
        };
      }
    }

    return {
      backgroundColor: backgroundColor,
      color: textColor,
      border: '1px solid rgba(0,0,0,0.1)'
    };
  };

  return (
    <span
      className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium mr-1 mb-1 relative cursor-pointer group"
      style={getTagStyle()}
      onMouseEnter={() => setShowTooltip(true)} // 恢复：仅显示工具提示，不触发滑动
      onMouseLeave={() => setShowTooltip(false)} // 恢复：仅隐藏工具提示，不触发滑动
    >
      {text}

      {/* 悬停时显示的删除按钮 - 位于右上角 */}
      <button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onRemove();
        }}
        className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
        style={{ fontSize: '8px', lineHeight: '1' }}
        title="删除词语"
      >
        ×
      </button>

      {/* 工具提示 - 恢复：仅显示提示信息，不触发滑动 */}
      {showTooltip && (isDuplicate || !isValid) && (
        <div className="absolute bottom-full left-0 mb-1 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap z-10">
          {!isValid && '格式错误'}
          {isDuplicate && `重复于: ${duplicateLibraries.join(', ')}`}
        </div>
      )}
    </span>
  );
};

// ===== 主组件 =====

const WordInput: React.FC<WordInputProps> = ({
  libraryKey,
  color,
  collapsed = false,
  placeholder = '输入词语，用逗号分隔...',
  className = '',
  onChange,
  onValidation
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const inputTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [inputValue, setInputValue] = useState('');
  const [words, setWords] = useState<string[]>([]);
  const [validationResults, setValidationResults] = useState<Map<string, WordValidationResult>>(new Map());
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [showError, setShowError] = useState(false);

  // 获取词库状态
  const {
    getLibrary,
    validateInput,
    checkCrossLibraryDuplicate,
    addWord,
    removeWord,
    getWordHighlightColor,
    setWordHighlightColor
  } = useWordLibraryStore();

  const library = getLibrary(libraryKey);
  const backgroundColor = getWordLibraryBackgroundColor(color);
  const textColor = getWordLibraryTextColor(backgroundColor);

  // 加载现有词语
  useEffect(() => {
    if (library) {
      const existingWords = library.words.map(word => word.text);
      setWords(existingWords);
    }
  }, [library]);

  // 验证词语（仅用于UI显示，不阻止输入）
  const validateWords = useCallback((wordList: string[]) => {
    const results = new Map<string, WordValidationResult>();

    wordList.forEach(word => {
      // 使用新的输入验证
      const inputValidation = validateInput(libraryKey, word);
      const crossLibraryCheck = checkCrossLibraryDuplicate(word);

      results.set(word, {
        isValid: inputValidation.isValid,
        errors: inputValidation.errors,
        isDuplicate: crossLibraryCheck.isDuplicate,
        duplicateLibraries: crossLibraryCheck.duplicateLibraries
      });
    });

    setValidationResults(results);
    onValidation?.(Array.from(results.values()));
  }, [libraryKey, validateInput, checkCrossLibraryDuplicate, onValidation]);

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  // 显示错误提示
  const showErrorMessage = useCallback((message: string) => {
    setErrorMessage(message);
    setShowError(true);

    // 3秒后自动隐藏 - 使用防抖延迟常量
    setTimeout(() => {
      setShowError(false);
    }, 3000); // 使用常量 DEBOUNCE_DELAYS.VERY_SLOW * 3
  }, []);

  // 处理输入确认（逗号或回车）
  const handleInputConfirm = useCallback(() => {
    if (!inputValue.trim()) return;

    const newWords = parseInputText(inputValue);
    const validWords: string[] = [];
    const errorWords: string[] = [];

    // 逐个验证和添加词语
    newWords.forEach(word => {
      const validation = validateInput(libraryKey, word);
      if (validation.isValid) {
        // 验证通过，添加到词库
        const result = addWord(libraryKey, word);
        if (result.isValid) {
          validWords.push(word);
        }
      } else {
        // 验证失败，收集错误词语
        errorWords.push(word);
        console.warn(`词语"${word}"验证失败:`, validation.errors);
      }
    });

    // 显示错误提示
    if (errorWords.length > 0) {
      if (errorWords.length === 1) {
        const validation = validateInput(libraryKey, errorWords[0]);
        const errorMsg = validation.errors[0] || '输入无效';
        showErrorMessage(errorMsg);
      } else {
        showErrorMessage(`${errorWords.length}个词语输入无效，请检查格式或重复`);
      }
    }

    // 只更新成功添加的词语
    if (validWords.length > 0) {
      const updatedWords = [...words, ...validWords];
      setWords(updatedWords);
      validateWords(updatedWords);
      onChange?.(updatedWords);
    }

    setInputValue('');
  }, [inputValue, words, validateWords, onChange, addWord, libraryKey, validateInput, showErrorMessage]);

  // 使用防抖检测逗号并自动切割文本
  const debouncedAutoConfirm = useCallback(() => {
    if (inputValue.includes('，') || inputValue.includes(',')) {
      handleInputConfirm();
    }
  }, [inputValue, handleInputConfirm]);

  // 创建防抖函数 - 临时使用简单实现
  useEffect(() => {
    // 清除之前的定时器
    if (inputTimerRef.current) {
      clearTimeout(inputTimerRef.current);
    }

    // 如果输入为空，不设置定时器
    if (!inputValue.trim()) {
      inputTimerRef.current = null;
      return;
    }

    // 使用防抖延迟 - 1秒后自动确认
    const timer = setTimeout(() => {
      debouncedAutoConfirm();
    }, 1000); // 使用常量 DEBOUNCE_DELAYS.VERY_SLOW

    inputTimerRef.current = timer;

    // 清理函数
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [inputValue, debouncedAutoConfirm]);

  // 组件卸载时的清理逻辑
  useEffect(() => {
    return () => {
      // 清理所有定时器
      if (inputTimerRef.current) {
        clearTimeout(inputTimerRef.current);
        inputTimerRef.current = null;
      }
      console.log('WordInput组件卸载，清理定时器');
    };
  }, []);

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      handleInputConfirm();
    } else if (e.key === 'Backspace' && !inputValue && words.length > 0) {
      // 删除最后一个词语
      const newWords = words.slice(0, -1);
      setWords(newWords);
      validateWords(newWords);
      onChange?.(newWords);
    }
  };

  // 删除词语
  const handleRemoveWord = (index: number) => {
    const wordToRemove = words[index];
    const newWords = words.filter((_, i) => i !== index);

    setWords(newWords);
    validateWords(newWords);
    onChange?.(newWords);

    // 从词库中删除
    if (library) {
      const wordEntry = library.words.find(w => w.text === wordToRemove);
      if (wordEntry) {
        removeWord(libraryKey, wordEntry.id);
      }
    }
  };

  // 折叠状态下的显示
  if (collapsed) {
    return (
      <div
        className={`flex items-center overflow-x-auto py-2 px-3 rounded-md ${className}`}
        style={{ backgroundColor, color: textColor }}
      >
        <div className="flex flex-nowrap space-x-1">
          {words.slice(0, 5).map((word, index) => (
            <span key={index} className="whitespace-nowrap text-sm">
              {word}
            </span>
          ))}
          {words.length > 5 && (
            <span className="text-sm opacity-70">
              +{words.length - 5}
            </span>
          )}
        </div>
      </div>
    );
  }

  // 展开状态下的完整输入框
  return (
    <div
      className={`border rounded-md p-2 min-h-[80px] ${className}`}
      style={{
        backgroundColor,
        color: textColor,
        borderColor: 'rgba(0,0,0,0.2)'
      }}
    >
      {/* 词语标签 */}
      <div className="flex flex-wrap mb-2">
        {words.map((word, index) => {
          const validation = validationResults.get(word);
          const isDuplicate = validation?.isDuplicate ?? false;
          const duplicateLibraries = validation?.duplicateLibraries ?? [];

          // 如果是跨词库重复，获取或生成高亮颜色
          let highlightColor: string | undefined;
          if (isDuplicate && duplicateLibraries.length > 0 && !duplicateLibraries.includes(libraryKey)) {
            highlightColor = getWordHighlightColor(word);
            if (!highlightColor) {
              // 生成新的随机颜色
              const { assignWordHighlightColor } = require('@/core/wordLibrary/WordLibraryCore');
              const state = useWordLibraryStore.getState();
              const newColor = assignWordHighlightColor(state.usedHighlightColors);
              highlightColor = newColor;
              setWordHighlightColor(word, newColor);
            }
          }

          return (
            <WordTag
              key={`${word}-${index}`}
              text={word}
              isValid={validation?.isValid ?? true}
              isDuplicate={isDuplicate}
              duplicateLibraries={duplicateLibraries}
              backgroundColor={backgroundColor}
              textColor={textColor}
              highlightColor={highlightColor}
              onRemove={() => handleRemoveWord(index)}
            />
          );
        })}
      </div>

      {/* 输入框 */}
      <input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onBlur={handleInputConfirm}
        placeholder={words.length === 0 ? placeholder : '继续输入...'}
        className="w-full bg-transparent border-none outline-none text-sm"
        style={{ color: textColor }}
      />

      {/* 提示信息 */}
      <div className="text-xs opacity-70 mt-1">
        {words.length} 个词语 | 用逗号分隔或按回车确认
      </div>

      {/* 错误提示 */}
      {showError && (
        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm shadow-sm">
          <div className="flex items-center space-x-2">
            <span className="text-red-500">❌</span>
            <span className="font-medium">{errorMessage}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default WordInput;
export type { WordInputProps };
