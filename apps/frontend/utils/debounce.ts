/**
 * 防抖工具函数
 * 🎯 核心价值：提供专业的防抖实现，避免频繁触发
 * 📦 功能范围：基础防抖、带取消功能的防抖、立即执行防抖
 * 🔄 架构设计：轻量级工具函数，无外部依赖
 */

// ===== 类型定义 =====

export type DebouncedFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): void;
  cancel: () => void;
  flush: () => void;
};

// ===== 基础防抖函数 =====

/**
 * 创建防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行（首次调用时）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate = false
): DebouncedFunction<T> {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastArgs: Parameters<T> | null = null;
  let lastThis: any = null;

  const debouncedFunction = function (this: any, ...args: Parameters<T>) {
    lastArgs = args;
    lastThis = this;

    const callNow = immediate && !timeoutId;

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 设置新的定时器
    timeoutId = setTimeout(() => {
      timeoutId = null;
      if (!immediate) {
        func.apply(lastThis, lastArgs!);
      }
    }, delay);

    // 如果是立即执行模式且是首次调用，立即执行
    if (callNow) {
      func.apply(this, args);
    }
  };

  // 取消防抖
  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    lastArgs = null;
    lastThis = null;
  };

  // 立即执行待执行的函数
  debouncedFunction.flush = () => {
    if (timeoutId && lastArgs) {
      clearTimeout(timeoutId);
      func.apply(lastThis, lastArgs);
      timeoutId = null;
      lastArgs = null;
      lastThis = null;
    }
  };

  return debouncedFunction as DebouncedFunction<T>;
}

// ===== 异步防抖函数 =====

/**
 * 创建异步防抖函数
 * @param func 要防抖的异步函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的异步函数
 */
export function debounceAsync<T extends (...args: any[]) => Promise<any>>(
  func: T,
  delay: number
): DebouncedFunction<T> {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastArgs: Parameters<T> | null = null;
  let lastThis: any = null;

  const debouncedFunction = function (this: any, ...args: Parameters<T>) {
    lastArgs = args;
    lastThis = this;

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 设置新的定时器
    timeoutId = setTimeout(async () => {
      timeoutId = null;
      try {
        await func.apply(lastThis, lastArgs!);
      } catch (error) {
        console.error('Debounced async function error:', error);
      }
    }, delay);
  };

  // 取消防抖
  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    lastArgs = null;
    lastThis = null;
  };

  // 立即执行待执行的函数
  debouncedFunction.flush = async () => {
    if (timeoutId && lastArgs) {
      clearTimeout(timeoutId);
      try {
        await func.apply(lastThis, lastArgs);
      } catch (error) {
        console.error('Debounced async function flush error:', error);
      }
      timeoutId = null;
      lastArgs = null;
      lastThis = null;
    }
  };

  return debouncedFunction as DebouncedFunction<T>;
}

// ===== React Hook 版本 =====

import { useCallback, useRef, useEffect } from 'react';

/**
 * React Hook 版本的防抖
 * @param callback 要防抖的回调函数
 * @param delay 延迟时间（毫秒）
 * @param deps 依赖数组
 * @returns 防抖后的函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList = []
): DebouncedFunction<T> {
  const debouncedFn = useRef<DebouncedFunction<T>>();

  const debouncedCallback = useCallback(callback, deps);

  // 创建或更新防抖函数
  useEffect(() => {
    // 取消之前的防抖函数
    if (debouncedFn.current) {
      debouncedFn.current.cancel();
    }

    // 创建新的防抖函数
    debouncedFn.current = debounce(debouncedCallback, delay);

    return () => {
      if (debouncedFn.current) {
        debouncedFn.current.cancel();
      }
    };
  }, [debouncedCallback, delay]);

  // 清理函数
  useEffect(() => {
    return () => {
      if (debouncedFn.current) {
        debouncedFn.current.cancel();
      }
    };
  }, []);

  return debouncedFn.current!;
}

// ===== 常用配置 =====

export const DEBOUNCE_DELAYS = {
  FAST: 100,      // 快速防抖 - 用于用户输入
  NORMAL: 300,    // 正常防抖 - 用于搜索
  SLOW: 500,      // 慢速防抖 - 用于API调用
  VERY_SLOW: 1000 // 很慢防抖 - 用于自动保存
} as const;
